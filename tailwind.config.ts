import type { Config } from "tailwindcss";
const config: Config = {
  darkMode: ["class"],
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    container: {
      center: true,
      padding: "1.5rem",
    },
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        /* base gradient */
        'start-gradient': 'var(--start-gradient)',
        'end-gradient': 'var(--end-gradient)',

        /* primary colors */
        'primary-01': 'var(--primary-01)',
        'primary-02': 'var(--primary-02)',
        'primary-03': 'var(--primary-03)',

        'gray-01': 'var(--gray-01)',
        'gray-02': 'var(--gray-02)',

        /* primary background colors */
        'primary-bg': 'var(--primary-bg)',
        'secondary-bg': 'var(--secondary-bg)',
        'primary-02-bg': 'var(--primary-02-bg)',
        'primary-03-bg': 'var(--primary-03-bg)',

        /* action background colors */
        'action-01-bg': 'var(--action-01-bg)',
        'action-02-bg': 'var(--action-02-bg)',
        'action-03-bg': 'var(--action-03-bg)',
        'action-04-bg': 'var(--action-04-bg)',
        'action-05-bg': 'var(--action-05-bg)',
        'action-06-bg': 'var(--action-06-bg)',

        /* action colors */
        'action-01': 'var(--action-01)',
        'action-02': 'var(--action-02)',
        'action-03': 'var(--action-03)',
        'action-04': 'var(--action-04)',
        'action-05': 'var(--action-05)',
        'action-06': 'var(--action-06)',

        /* button gradient */
        'btn-gradient-01': 'var(--btn-gradient-01)',
        'btn-gradient-02': 'var(--btn-gradient-02)',
        'btn-gradient-03': 'var(--btn-gradient-03)',
        'btn-gradient-04': 'var(--btn-gradient-04)',
        'btn-gradient-05': 'var(--btn-gradient-05)',

        /* card shadow */
        'card-shadow-color': 'var(--card-shadow-color)',

        primary: {
          DEFAULT: "var(--color-primary-500)",
          50: "var(--color-primary-50)",
          100: "var(--color-primary-100)",
          200: "var(--color-primary-200)",
          300: "var(--color-primary-300)",
          400: "var(--color-primary-400)",
          500: "var(--color-primary-500)",
          600: "var(--color-primary-600)",
          700: "var(--color-primary-700)",
          800: "var(--color-primary-800)",
          900: "var(--color-primary-900)",
        },
        gray: {
          DEFAULT: "var(--color-gray-300)",
          50: "var(--color-gray-50)",
          100: "var(--color-gray-100)",
          200: "var(--color-gray-200)",
          300: "var(--color-gray-300)",
          400: "var(--color-gray-400)",
          500: "var(--color-gray-500)",
          600: "var(--color-gray-600)",
          700: "var(--color-gray-700)",
          800: "var(--color-gray-800)",
          900: "var(--color-gray-900)",
        },
        success: {
          DEFAULT: "var(--color-success-500)",
          100: "var(--color-success-100)",
          200: "var(--color-success-200)",
          300: "var(--color-success-300)",
          400: "var(--color-success-400)",
          500: "var(--color-success-500)",
          600: "var(--color-success-600)",
          700: "var(--color-success-700)",
          800: "var(--color-success-800)",
          900: "var(--color-success-900)",
        },
        error: {
          DEFAULT: "var(--color-error-500)",
          100: "var(--color-error-100)",
          200: "var(--color-error-200)",
          300: "var(--color-error-300)",
          400: "var(--color-error-400)",
          500: "var(--color-error-500)",
          600: "var(--color-error-600)",
          700: "var(--color-error-700)",
          800: "var(--color-error-800)",
          900: "var(--color-error-900)",
        },
        warning: {
          DEFAULT: "var(--color-warning-500)",
          100: "var(--color-warning-100)",
          200: "var(--color-warning-200)",
          300: "var(--color-warning-300)",
          400: "var(--color-warning-400)",
          500: "var(--color-warning-500)",
          600: "var(--color-warning-600)",
          700: "var(--color-warning-700)",
          800: "var(--color-warning-800)",
          900: "var(--color-warning-900)",
        },
        info: {
          DEFAULT: "var(--color-info-500)",
          100: "var(--color-info-100)",
          200: "var(--color-info-200)",
          300: "var(--color-info-300)",
          400: "var(--color-info-400)",
          500: "var(--color-info-500)",
          600: "var(--color-info-600)",
          700: "var(--color-info-700)",
          800: "var(--color-info-800)",
          900: "var(--color-info-900)",
        },

        secondary: "var(--color-secondary)",

        label: "var(--color-label)",
        disabled: "var(--color-disabled)",
        typography: "var(--color-typography)",
        placeholder: "var(--color-placeholder)",

        card: "var(--color-bg-card)",
        paper: "var(--color-bg-paper)",
      },
      boxShadow: {
        "3xl": "0px 2px 4px -2px #2A334208",
        "4xl": "0px 1px 2px 0px #0000000F",
      },
    },
  },
  plugins: [],
};
export default config;
